#!/usr/bin/env python3
"""
Generate a secure secret key for Flask production deployment
"""

import secrets
import string

def generate_secret_key(length=64):
    """Generate a cryptographically secure secret key."""
    # Use a combination of letters, digits, and some safe special characters
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*(-_=+)"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def main():
    print("🔐 FLASK SECRET KEY GENERATOR")
    print("=" * 50)
    
    # Generate a 64-character secret key
    secret_key = generate_secret_key(64)
    
    print(f"Your secure secret key:")
    print(f"SECRET_KEY={secret_key}")
    print()
    print("📋 Copy this key and replace 'your-super-secret-key-here...' in your .env.render file")
    print("⚠️  Keep this key secret and never commit it to version control!")
    print("🔒 This key is used for:")
    print("   - Session encryption")
    print("   - JWT token signing") 
    print("   - CSRF token generation")
    print("   - Cookie security")

if __name__ == "__main__":
    main()
