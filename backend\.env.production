# Production Environment Variables Template
# Copy these to your Render service environment variables

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False

# Database Configuration
# DATABASE_URL will be automatically set by Render PostgreSQL service

# Security
# SECRET_KEY will be auto-generated by Render

# Firebase Configuration (Set these in Render dashboard)
# FIREBASE_CREDENTIALS_JSON={"type":"service_account",...}
# FIREBASE_STORAGE_BUCKET=your-bucket-name.firebasestorage.app

# CORS Configuration
# FRONTEND_URL=https://your-frontend-domain.com
# FIREBASE_HOSTING_DOMAIN=https://your-project.web.app

# Admin User
# ADMIN_EMAIL=<EMAIL>

# Logging
LOG_FILE=/tmp/app.log

# Cache Configuration (Redis recommended for production)
CACHE_TYPE=redis
# REDIS_URL will be automatically set by Render Redis service
CACHE_TIMEOUT=3600
CACHE_KEY_PREFIX=customer_mgmt_prod_

# Rate Limiting
RATE_LIMIT_ENABLED=True

# Security Settings
SANITIZE_RESPONSES=True

