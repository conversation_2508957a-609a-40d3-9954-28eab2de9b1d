# =============================================================================
# RENDER PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains all environment variables needed for Render deployment
# Upload this file in Render Dashboard > Environment > Import from .env file

# =============================================================================
# FLASK CONFIGURATION
# =============================================================================
FLASK_ENV=production
FLASK_DEBUG=False

# Generate a strong secret key for production
# You can generate one with: python -c "import secrets; print(secrets.token_hex(32))"
SECRET_KEY=yA14vg^=36POxWo22X+R_Dcppx)i&pIw)eU*_UESLl5YMMEpTl2RSRO7bT9&vl9wPJ=

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# This will be automatically set by Render PostgreSQL service
# Format: postgresql://username:password@host:port/database
DATABASE_URL=

# =============================================================================
# FIREBASE CONFIGURATION
# =============================================================================
# Get these from Firebase Console > Project Settings > Service Accounts
# Download the JSON file and paste the entire content as one line
FIREBASE_CREDENTIALS_JSON=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************


# Firebase Storage bucket name (from Firebase Console > Storage)
FIREBASE_STORAGE_BUCKET=amspmdeploy.firebasestorage.app

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
# Your frontend URL for CORS configuration
FRONTEND_URL=https://your-frontend-domain.com

# Firebase Hosting domain (if using Firebase Hosting)
FIREBASE_HOSTING_DOMAIN=your-project-id.web.app

# =============================================================================
# ADMIN USER CONFIGURATION
# =============================================================================
# Email address for the admin user (will be created automatically)
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log file path (Render uses /tmp for temporary files)
LOG_FILE=/tmp/app.log

# =============================================================================
# CACHE CONFIGURATION (SimpleCache - No Redis)
# =============================================================================
# Cache type - using SimpleCache to avoid Redis costs
CACHE_TYPE=SimpleCache

# Cache timeout in seconds (300 = 5 minutes)
CACHE_TIMEOUT=300

# Cache key prefix to avoid conflicts
CACHE_KEY_PREFIX=customer_mgmt_prod_

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# Enable rate limiting for API protection
RATE_LIMIT_ENABLED=True

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Enable response sanitization to prevent data leaks
SANITIZE_RESPONSES=True

# =============================================================================
# OPTIONAL: E-BOEKHOUDEN INTEGRATION
# =============================================================================
# Uncomment and fill these if you want to integrate with e-Boekhouden
# EBOEKHOUDEN_API_KEY=your-api-key
# EBOEKHOUDEN_API_URL=https://api.e-boekhouden.nl
# EBOEKHOUDEN_API_USERNAME=your-username
# EBOEKHOUDEN_API_PASSWORD=your-password

# =============================================================================
# RENDER-SPECIFIC CONFIGURATION
# =============================================================================
# Port will be automatically set by Render
# PORT=10000

# Python version (Render will detect this automatically)
# PYTHON_VERSION=3.11

# =============================================================================
# INSTRUCTIONS FOR SETUP
# =============================================================================
# 1. Replace 'your-super-secret-key-here...' with a real 64-character secret key
# 2. Replace Firebase credentials with your actual Firebase service account JSON
# 3. Replace 'your-project-id.appspot.com' with your actual Firebase Storage bucket
# 4. Replace 'https://your-frontend-domain.com' with your actual frontend URL
# 5. Replace '<EMAIL>' with your actual admin email
# 6. DATABASE_URL will be automatically set by Render PostgreSQL service
# 7. Upload this file in Render Dashboard > Environment > Import from .env file
